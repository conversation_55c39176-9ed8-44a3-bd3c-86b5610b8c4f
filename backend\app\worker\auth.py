import os
from fastapi import Header, HTTPException, status, Depends


def require_api_key(x_api_key: str | None = Header(default=None, alias="X-API-Key")) -> str:
    expected = os.getenv("API_KEY")
    if not expected:
        # If not configured, allow all for easier local dev
        return ""
    if not x_api_key or x_api_key != expected:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid API key")
    return x_api_key


def optional_api_key(x_api_key: str | None = Header(default=None, alias="X-API-Key")) -> str | None:
    return x_api_key


