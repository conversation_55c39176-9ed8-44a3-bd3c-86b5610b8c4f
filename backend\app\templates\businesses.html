<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Directory</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .nav {
            margin-bottom: 20px;
        }
        .nav a {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-right: 10px;
        }
        .nav a:hover {
            background: #0056b3;
        }
        .business-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }
        .business-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .business-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .business-id {
            background: #007bff;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }
        .business-url {
            color: #666;
            margin-bottom: 10px;
            word-break: break-all;
        }
        .business-url a {
            color: #007bff;
            text-decoration: none;
        }
        .business-url a:hover {
            text-decoration: underline;
        }
        .business-stats {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }
        .stat {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #666;
            font-size: 14px;
        }
        .stat-number {
            background: #e9ecef;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: bold;
            color: #495057;
        }
        .business-meta {
            margin-top: 10px;
            font-size: 12px;
            color: #999;
        }
        .search-box {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .search-box input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .stats-summary {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-item-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-item-label {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Business Directory</h1>
        <div class="nav">
            <a href="/ui">Add Source</a>
            <a href="/ui/manage">Manage Sources</a>
            <a href="/ui/businesses">View Businesses</a>
            <a href="/api/businesses">API View</a>
        </div>
    </div>

    <div class="search-box">
        <input type="text" id="searchInput" placeholder="Search businesses by name or ID..." onkeyup="filterBusinesses()">
    </div>

    <div class="stats-summary">
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-item-number">{{ business_data|length }}</div>
                <div class="stat-item-label">Total Businesses</div>
            </div>
            <div class="stat-item">
                <div class="stat-item-number">{{ business_data|sum(attribute='tour_count') }}</div>
                <div class="stat-item-label">Total Tours</div>
            </div>
            <div class="stat-item">
                <div class="stat-item-number">{{ business_data|sum(attribute='source_count') }}</div>
                <div class="stat-item-label">Total Sources</div>
            </div>
            <div class="stat-item">
                <div class="stat-item-number">{{ business_data|selectattr('tour_count', 'gt', 0)|list|length }}</div>
                <div class="stat-item-label">With Tours</div>
            </div>
        </div>
    </div>

    <div id="businessList">
        {% for data in business_data %}
        <div class="business-card" data-name="{{ data.business.name.lower() }}" data-id="{{ data.business.id }}">
            <div class="business-header">
                <div class="business-name">{{ data.business.name }}</div>
                <div class="business-id">ID: {{ data.business.id }}</div>
            </div>
            
            {% if data.business.website_url %}
            <div class="business-url">
                <a href="{{ data.business.website_url }}" target="_blank" rel="noopener">{{ data.business.website_url }}</a>
            </div>
            {% endif %}
            
            {% if data.business.description %}
            <div style="color: #666; margin-bottom: 10px;">{{ data.business.description }}</div>
            {% endif %}
            
            {% if data.business.category %}
            <div style="color: #666; margin-bottom: 10px;"><strong>Category:</strong> {{ data.business.category }}</div>
            {% endif %}
            
            <div class="business-stats">
                <div class="stat">
                    <span>Tours:</span>
                    <span class="stat-number">{{ data.tour_count }}</span>
                </div>
                <div class="stat">
                    <span>Sources:</span>
                    <span class="stat-number">{{ data.source_count }}</span>
                </div>
            </div>
            
            <div class="business-meta">
                Created: {{ data.business.created_at.strftime('%Y-%m-%d %H:%M') if data.business.created_at else 'N/A' }}
            </div>
        </div>
        {% endfor %}
    </div>

    {% if not business_data %}
    <div class="business-card">
        <div class="business-name">No businesses found</div>
        <p>No businesses have been created yet. <a href="/ui">Add your first source</a> to get started.</p>
    </div>
    {% endif %}

    <script>
        function filterBusinesses() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const businessCards = document.querySelectorAll('.business-card');
            
            businessCards.forEach(card => {
                const name = card.getAttribute('data-name');
                const id = card.getAttribute('data-id');
                
                if (name.includes(searchTerm) || id.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>
