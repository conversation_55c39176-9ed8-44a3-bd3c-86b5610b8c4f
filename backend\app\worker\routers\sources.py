from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from ..auth import require_api_key
from ..db import get_db
from .. import models, schemas


router = APIRouter(prefix="/api/sources", tags=["sources"], dependencies=[Depends(require_api_key)])


@router.post("/", response_model=schemas.ScrapeSourceRead)
def create_source(payload: schemas.ScrapeSourceCreate, db: Session = Depends(get_db)):
    source = models.ScrapeSource(
        business_id=payload.business_id,
        business_name=payload.business_name,
        start_url=str(payload.start_url),
        is_active=payload.is_active,
        schedule_minutes=payload.schedule_minutes,
    )
    db.add(source)
    db.commit()
    db.refresh(source)
    return source


@router.get("/", response_model=list[schemas.ScrapeSourceRead])
def list_sources(only_active: bool = True, db: Session = Depends(get_db)):
    q = db.query(models.ScrapeSource)
    if only_active:
        q = q.filter(models.ScrapeSource.is_active.is_(True))
    return q.order_by(models.ScrapeSource.created_at.desc()).all()


@router.get("/{source_id}", response_model=schemas.ScrapeSourceRead)
def get_source(source_id: int, db: Session = Depends(get_db)):
    source = db.query(models.ScrapeSource).get(source_id)
    if not source:
        raise HTTPException(status_code=404, detail="Source not found")
    return source


@router.put("/{source_id}", response_model=schemas.ScrapeSourceRead)
def update_source(source_id: int, payload: schemas.ScrapeSourceUpdate, db: Session = Depends(get_db)):
    source = db.query(models.ScrapeSource).get(source_id)
    if not source:
        raise HTTPException(status_code=404, detail="Source not found")

    # Update fields if provided
    if payload.business_name is not None:
        source.business_name = payload.business_name
    if payload.start_url is not None:
        source.start_url = str(payload.start_url)
    if payload.is_active is not None:
        source.is_active = payload.is_active
    if payload.schedule_minutes is not None:
        source.schedule_minutes = payload.schedule_minutes

    db.add(source)
    db.commit()
    db.refresh(source)
    return source


@router.delete("/{source_id}")
def delete_source(source_id: int, db: Session = Depends(get_db)):
    source = db.query(models.ScrapeSource).get(source_id)
    if not source:
        raise HTTPException(status_code=404, detail="Source not found")

    try:
        # If there's an associated business, delete its tours first
        if source.business_id:
            business = db.query(models.Business).get(source.business_id)
            if business:
                # Delete tours associated with this business
                db.query(models.Tour).filter(models.Tour.business_id == source.business_id).delete()
                # Delete the business
                db.delete(business)

        # Delete the source
        db.delete(source)
        db.commit()
        return {"message": "Source deleted successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error deleting source: {str(e)}")


@router.post("/{source_id}/toggle", response_model=schemas.ScrapeSourceRead)
def toggle_source(source_id: int, db: Session = Depends(get_db)):
    source = db.query(models.ScrapeSource).get(source_id)
    if not source:
        raise HTTPException(status_code=404, detail="Source not found")
    source.is_active = not source.is_active
    db.add(source)
    db.commit()
    db.refresh(source)
    return source


