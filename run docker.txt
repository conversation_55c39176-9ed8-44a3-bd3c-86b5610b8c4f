cmd into the folder, then run to create the container;


docker compose up -d




The app provides REST API endpoints to access the data:

Businesses:
http://localhost:8000/api/businesses - List all businesses
http://localhost:8000/api/businesses/{id} - Get specific business

Tours:
http://localhost:8000/api/tours - List all tours
http://localhost:8000/api/tours/{id} - Get specific tour

Sources (what you add via UI):
http://localhost:8000/api/sources - List all scrape sources

Via the Web UI
http://localhost:8000/ui to see the form and recent sources table




Run the docker database:
docker exec -it businessscrapperapp-db-1 psql -U appuser -d appdb


Here are some useful commands you can enter at the `appdb=#` prompt:

## Basic Database Commands;

1. List all tables:

\dt


2. See table structure:

\d scrape_sources
\d businesses  
\d tours


3. View data from tables:

-- See all scrape sources (what you add via the UI)
SELECT * FROM scrape_sources;

-- See all businesses
SELECT * FROM businesses;

-- See all tours (scraped data)
SELECT * FROM tours;


4. Count records:

SELECT COUNT(*) FROM scrape_sources;
SELECT COUNT(*) FROM businesses;
SELECT COUNT(*) FROM tours;


5. Exit the database:

\q


## Example Queries to See Your Data;

Check what sources you've added via the UI:

SELECT business_name, start_url, is_active, created_at, last_run_at 
FROM scrape_sources 
ORDER BY created_at DESC;


Check if any tours have been scraped:

SELECT t.title, t.location, t.price, b.name as business_name, t.last_updated
FROM tours t
JOIN businesses b ON t.business_id = b.id
ORDER BY t.last_updated DESC;


Check business information:

SELECT name, website_url, description, category, created_at
FROM businesses
ORDER BY created_at DESC;


check logs:
docker logs businessscrapperapp-worker-1


---


On Mac run Monoloci.app:

cd "/Volumes/T7 Shield/Monoloci/Monoloci_app_3"
npx http-server -p 8080
Or
npx http-server dist -p 8080

Run Scraper:

cd "/Volumes/T7 Shield/Business Scraper App"
npx http-server -p 8080