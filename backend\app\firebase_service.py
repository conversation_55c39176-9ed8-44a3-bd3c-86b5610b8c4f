import os
import json
from typing import Dict, Any, Optional
from datetime import datetime

import firebase_admin
from firebase_admin import credentials, firestore
from sqlalchemy.orm import Session

from . import models


class FirebaseService:
    """Service for syncing tour data to Firebase Firestore"""
    
    def __init__(self):
        self.db = None
        self.app_id = os.getenv("APP_ID", "monoloci")
        self._initialize_firebase()
    
    def _initialize_firebase(self):
        """Initialize Firebase Admin SDK"""
        try:
            service_account_path = os.getenv("FIREBASE_SERVICE_ACCOUNT_PATH")
            
            if not service_account_path:
                print("Warning: FIREBASE_SERVICE_ACCOUNT_PATH not set. Firebase sync disabled.")
                return
            
            if not os.path.exists(service_account_path):
                print(f"Warning: Firebase service account file not found at {service_account_path}")
                return
            
            # Initialize Firebase Admin SDK
            if not firebase_admin._apps:
                cred = credentials.Certificate(service_account_path)
                firebase_admin.initialize_app(cred)
            
            self.db = firestore.client()
            print(f"Firebase initialized successfully for app: {self.app_id}")
            
        except Exception as e:
            print(f"Error initializing Firebase: {e}")
            self.db = None
    
    def is_enabled(self) -> bool:
        """Check if Firebase service is properly initialized"""
        return self.db is not None
    
    def sync_tour_to_firebase(self, tour: models.Tour) -> bool:
        """
        Sync a single tour to Firebase
        Path: artifacts/{app_id}/public/data/tours/{business_id}/{tour_id}
        """
        if not self.is_enabled():
            return False
        
        try:
            # Prepare tour data for Firebase
            tour_data = {
                "id": tour.id,
                "title": tour.title,
                "location": tour.location,
                "start_date": tour.start_date.isoformat() if tour.start_date else None,
                "end_date": tour.end_date.isoformat() if tour.end_date else None,
                "price": float(tour.price) if tour.price else None,
                "url": tour.url,
                "last_updated": tour.last_updated.isoformat(),
                "business_name": tour.business.name,
                "business_website": tour.business.website_url,
                "business_category": tour.business.category,
                "tour_business_id": tour.business_id,  # The field you mentioned adding
                "synced_at": datetime.now().isoformat()
            }
            
            # Remove None values to keep Firebase data clean
            tour_data = {k: v for k, v in tour_data.items() if v is not None}
            
            # Firebase path: artifacts/{app_id}/public/data/directory/{firebase_business_id}/tours/{tour_id}
            # Map business_id 1 to Firebase directory ID 6390 (you'll need to add more mappings)
            business_mapping = {
                1: "6390"  # business_id 1 maps to directory/6390
            }

            firebase_business_id = business_mapping.get(tour.business_id)
            if not firebase_business_id:
                print(f"No Firebase mapping found for business_id {tour.business_id}")
                return False

            # Write to existing business in directory
            doc_path = f"artifacts/{self.app_id}/public/data/directory/{firebase_business_id}/tours"
            doc_ref = self.db.collection(doc_path).document(str(tour.id))
            doc_ref.set(tour_data)
            
            print(f"Successfully synced tour {tour.id} to Firebase")
            return True
            
        except Exception as e:
            print(f"Error syncing tour {tour.id} to Firebase: {e}")
            return False
    
    def sync_business_tours_to_firebase(self, business_id: int, db_session: Session) -> int:
        """
        Sync all tours for a specific business to Firebase
        Returns number of successfully synced tours
        """
        if not self.is_enabled():
            return 0
        
        try:
            # Get all tours for the business with business relationship loaded
            tours = db_session.query(models.Tour).join(models.Business).filter(
                models.Tour.business_id == business_id
            ).all()
            
            synced_count = 0
            for tour in tours:
                if self.sync_tour_to_firebase(tour):
                    synced_count += 1
            
            print(f"Synced {synced_count}/{len(tours)} tours for business {business_id}")
            return synced_count
            
        except Exception as e:
            print(f"Error syncing tours for business {business_id}: {e}")
            return 0
    
    def sync_all_tours_to_firebase(self, db_session: Session) -> int:
        """
        Sync all tours from database to Firebase
        Returns number of successfully synced tours
        """
        if not self.is_enabled():
            return 0
        
        try:
            # Get all tours with their business data
            tours = db_session.query(models.Tour).join(models.Business).all()
            
            synced_count = 0
            for tour in tours:
                if self.sync_tour_to_firebase(tour):
                    synced_count += 1
            
            print(f"Synced {synced_count}/{len(tours)} total tours to Firebase")
            return synced_count
            
        except Exception as e:
            print(f"Error syncing all tours to Firebase: {e}")
            return 0
    
    def delete_tour_from_firebase(self, business_id: int, tour_id: int) -> bool:
        """Delete a tour from Firebase"""
        if not self.is_enabled():
            return False
        
        try:
            doc_path = f"artifacts/{self.app_id}/public/data/tours/{business_id}"
            doc_ref = self.db.collection(doc_path).document(str(tour_id))
            doc_ref.delete()
            
            print(f"Successfully deleted tour {tour_id} from Firebase")
            return True
            
        except Exception as e:
            print(f"Error deleting tour {tour_id} from Firebase: {e}")
            return False


# Global Firebase service instance
firebase_service = FirebaseService()
