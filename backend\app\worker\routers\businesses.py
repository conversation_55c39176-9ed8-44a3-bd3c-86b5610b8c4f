from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from ..db import get_db
from .. import models, schemas
from ..auth import require_api_key


router = APIRouter(prefix="/api/businesses", tags=["businesses"])


@router.post("/", response_model=schemas.BusinessRead, dependencies=[Depends(require_api_key)])
def create_business(payload: schemas.BusinessCreate, db: Session = Depends(get_db)):
    existing = db.query(models.Business).filter(models.Business.name == payload.name).first()
    if existing:
        raise HTTPException(status_code=409, detail="Business with this name already exists")
    business = models.Business(
        name=payload.name,
        website_url=str(payload.website_url) if payload.website_url else None,
        description=payload.description,
        category=payload.category,
    )
    db.add(business)
    db.commit()
    db.refresh(business)
    return business


@router.get("/", response_model=list[schemas.BusinessRead], dependencies=[Depends(require_api_key)])
def list_businesses(q: str | None = None, category: str | None = None, db: Session = Depends(get_db)):
    query = db.query(models.Business)
    if q:
        like = f"%{q}%"
        query = query.filter(models.Business.name.ilike(like))
    if category:
        query = query.filter(models.Business.category == category)
    return query.order_by(models.Business.name.asc()).all()


@router.get("/{business_id}", response_model=schemas.BusinessRead, dependencies=[Depends(require_api_key)])
def get_business(business_id: int, db: Session = Depends(get_db)):
    business = db.query(models.Business).get(business_id)
    if not business:
        raise HTTPException(status_code=404, detail="Not found")
    return business


