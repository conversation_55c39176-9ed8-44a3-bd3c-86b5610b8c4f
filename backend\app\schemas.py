from datetime import date, datetime
from typing import Optional

from pydantic import BaseModel, HttpUrl


class BusinessCreate(BaseModel):
    name: str
    website_url: Optional[HttpUrl] = None
    description: Optional[str] = None
    category: Optional[str] = None


class BusinessRead(BusinessCreate):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True


class TourCreate(BaseModel):
    business_id: int
    title: str
    location: Optional[str] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    price: Optional[float] = None
    url: Optional[HttpUrl] = None


class TourRead(BaseModel):
    id: int
    business_id: int
    title: str
    location: Optional[str] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    price: Optional[float] = None
    url: Optional[str] = None
    last_updated: datetime

    class Config:
        from_attributes = True

    def model_dump(self, **kwargs):
        """Override model_dump to exclude None price fields"""
        data = super().model_dump(**kwargs)
        if data.get('price') is None:
            data.pop('price', None)
        return data


class ScrapeSourceCreate(BaseModel):
    business_name: str
    start_url: HttpUrl
    business_id: Optional[int] = None
    is_active: bool = True
    schedule_minutes: int = 1440


class ScrapeSourceUpdate(BaseModel):
    business_name: Optional[str] = None
    start_url: Optional[HttpUrl] = None
    is_active: Optional[bool] = None
    schedule_minutes: Optional[int] = None


class ScrapeSourceRead(ScrapeSourceCreate):
    id: int
    created_at: datetime
    last_run_at: Optional[datetime] = None

    class Config:
        from_attributes = True


