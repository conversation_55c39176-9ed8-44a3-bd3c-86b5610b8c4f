from __future__ import annotations

import re
from dataclasses import dataclass
from datetime import datetime
from typing import Iterable, Optional

import httpx
from bs4 import BeautifulSoup  # type: ignore


@dataclass
class TourItem:
    title: str
    location: Optional[str]
    start_date: Optional[str]
    end_date: Optional[str]
    price: Optional[str]
    url: Optional[str]
    last_updated: datetime


async def fetch_html(url: str, timeout: float = 20.0) -> str:
    async with httpx.AsyncClient(timeout=timeout, follow_redirects=True) as client:
        resp = await client.get(url)
        resp.raise_for_status()
        return resp.text




def parse_example(html: str, base_url: str = "") -> Iterable[TourItem]:
    """Parse HTML for tour listings using realistic patterns for actual tour websites."""
    soup = BeautifulSoup(html, "html.parser")

    # Look for structured data (JSON-LD) first - many tour sites use this
    json_ld_scripts = soup.find_all('script', type='application/ld+json')
    for script in json_ld_scripts:
        try:
            import json
            data = json.loads(script.string)
            if isinstance(data, dict) and data.get('@type') in ['Product', 'TouristTrip', 'Event']:
                yield TourItem(
                    title=data.get('name', 'Tour'),
                    location=data.get('location', {}).get('name') if isinstance(data.get('location'), dict) else None,
                    start_date=None,
                    end_date=None,
                    price=str(data.get('offers', {}).get('price', '')) if data.get('offers') else None,
                    url=data.get('url'),
                    last_updated=datetime.now(),
                )
        except (json.JSONDecodeError, AttributeError):
            continue

    # Look for tour links directly - simpler and more effective approach
    print(f"DEBUG: Analyzing page for tour links...")

    # Find all links that look like tours
    links = soup.find_all('a', href=True)
    tour_links = []

    for link in links:
        text = link.get_text(strip=True)
        href = link.get('href', '')

        # Must have tour-related keywords
        tour_keywords = ['tour', 'day', 'experience', 'adventure', 'trip', 'excursion', 'package', 'activity', 'safari', 'cruise']
        if not any(kw in text.lower() for kw in tour_keywords):
            continue

        # Must be reasonable length
        if len(text) < 10 or len(text) > 200:
            continue

        # Skip navigation/menu items and generic links
        skip_keywords = ['menu', 'navigation', 'footer', 'header', 'contact', 'about', 'home', 'destinations', 'durations', 'browse', 'view all', 'see all', '@', 'mailto']
        if any(skip in text.lower() for skip in skip_keywords):
            continue

        # Skip email addresses
        if '@' in text or href.startswith('mailto:'):
            continue

        # Skip if it's just a number or very generic
        if text.isdigit() or text.lower() in ['tour', 'tours', 'day', 'days']:
            continue

        tour_links.append((text, href))

    print(f"DEBUG: Found {len(tour_links)} potential tour links")

    # Process each tour link as a separate tour
    found_tours = []

    for text, href in tour_links:
        # Extract price from the link text or surrounding context
        price_text = text
        price = None
        price_patterns = [
            r'\$\d+(?:,\d{3})*(?:\.\d{2})?',
            r'from\s*\$\d+',
            r'\d+\s*AUD',
            r'Price:\s*\$\d+',
            r'AUD\s*\$?\d+',
        ]
        for pattern_re in price_patterns:
            match = re.search(pattern_re, price_text, re.IGNORECASE)
            if match:
                # Extract just the numeric part
                price_str = re.sub(r'[^\d.]', '', match.group())
                try:
                    price = float(price_str)
                except ValueError:
                    price = None
                break

        # Extract location from title
        location = None
        text_lower = text.lower()

        # Look for Australian locations in the title
        location_keywords = [
            'adelaide', 'sydney', 'melbourne', 'brisbane', 'perth', 'darwin',
            'tasmania', 'queensland', 'nsw', 'victoria', 'south australia',
            'western australia', 'northern territory', 'canberra', 'hobart',
            'flinders ranges', 'outback', 'coober pedy', 'alice springs',
            'yorke peninsula', 'riverland', 'murray river', 'lake eyre', 'arkaroola'
        ]
        for keyword in location_keywords:
            if keyword in text_lower:
                location = keyword.title()
                break

        # Default to Adelaide for SA Eco Tours if no specific location found
        if not location and 'adelaide' not in text_lower:
            location = "Adelaide"

        # Make relative URLs absolute
        if href and href.startswith('/'):
            from urllib.parse import urljoin
            href = urljoin(base_url, href)

        found_tours.append(TourItem(
            title=text,
            location=location,
            start_date=None,
            end_date=None,
            price=price,
            url=href,
            last_updated=datetime.now(),
        ))

    print(f"DEBUG: Created {len(found_tours)} tour items")

    # Remove duplicates based on title
    seen_titles = set()
    for tour in found_tours:
        if tour.title not in seen_titles:
            seen_titles.add(tour.title)
            yield tour


