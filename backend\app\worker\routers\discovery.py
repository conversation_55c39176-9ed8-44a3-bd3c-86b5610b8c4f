from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from ..auth import require_api_key
from ..db import get_db
from ..worker.discovery import discover_au_tour_companies


router = APIRouter(prefix="/api/discovery", tags=["discovery"], dependencies=[Depends(require_api_key)])


@router.post("/run")
async def run_discovery(db: Session = Depends(get_db)):
    created = await discover_au_tour_companies(db)
    return {"created": created}


