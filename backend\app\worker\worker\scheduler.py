import asyncio
import os
import re
from datetime import datetime, timedelta
from typing import Awaitable, Callable

from sqlalchemy.orm import Session

from ..db import SessionLocal
from ..models import Business, ScrapeSource, Tour
from .scraper import fetch_html, parse_example
from .discovery import discover_au_tour_companies
from ..firebase_service import firebase_service


def _contains_tour_content(html: str) -> bool:
    """Check if HTML contains tour-related content."""
    html_lower = html.lower()

    # Look for tour-related keywords
    tour_keywords = [
        'tour', 'tours', 'experience', 'adventure', 'excursion', 'package',
        'activity', 'trip', 'journey', 'expedition', 'booking', 'book now'
    ]

    # Must have multiple tour keywords
    keyword_count = sum(1 for keyword in tour_keywords if keyword in html_lower)

    # Look for pricing indicators
    price_patterns = [
        r'\$\d+', r'from\s*\$', r'price', r'cost', r'aud', r'per person'
    ]
    has_pricing = any(re.search(pattern, html_lower) for pattern in price_patterns)

    # Look for booking/contact elements
    booking_indicators = ['book', 'reserve', 'contact', 'enquire', 'phone', 'email']
    has_booking = any(indicator in html_lower for indicator in booking_indicators)

    return keyword_count >= 3 and (has_pricing or has_booking)

async def run_periodic(interval_seconds: int, job: Callable[[Session], Awaitable[None]]) -> None:
    while True:
        with SessionLocal() as session:
            try:
                await job(session)
            except Exception as exc:  # noqa: BLE001
                print(f"[{datetime.now().isoformat()}] Job error: {exc}")
        await asyncio.sleep(interval_seconds)


def _parse_price_to_float(price_text: str | None) -> float | None:
    if not price_text:
        return None
    cleaned = "".join(ch for ch in price_text if ch.isdigit() or ch in ".,")
    cleaned = cleaned.replace(",", "")
    try:
        return float(cleaned)
    except Exception:
        return None


async def process_sources_job(db: Session) -> None:
    # Use local time - simple and straightforward
    now = datetime.now()
    sources = db.query(ScrapeSource).filter(ScrapeSource.is_active.is_(True)).all()
    for source in sources:
        last = source.last_run_at or source.created_at
        due_after = timedelta(minutes=source.schedule_minutes)
        if last and (now - last) < due_after:
            continue

        try:
            html = await fetch_html(source.start_url)

            # Validate that the page contains tour-related content (temporarily disabled for debugging)
            # if not _contains_tour_content(html):
            #     print(f"Skipping {source.start_url} - no tour content detected")
            #     continue

            items = list(parse_example(html, source.start_url))
            print(f"DEBUG: Source '{source.business_name}' found {len(items)} items")

            if items:
                print(f"DEBUG: First item: {items[0].title}")
                print(f"DEBUG: First item price: {items[0].price}")
                print(f"DEBUG: First item location: {items[0].location}")

            business: Business | None = None
            if source.business_id:
                business = db.query(Business).get(source.business_id)
            if not business:
                business = db.query(Business).filter(Business.name == source.business_name).first()
            if not business:
                business = Business(name=source.business_name, website_url=source.start_url)
                db.add(business)
                db.flush()
                # Update the source to reference the newly created business
                source.business_id = business.id
                print(f"[{now.isoformat()}] Created new business '{business.name}' with ID {business.id}")
            elif not source.business_id:
                # Link existing business to source if not already linked
                source.business_id = business.id
                print(f"[{now.isoformat()}] Linked source to existing business '{business.name}' with ID {business.id}")

            created_count = 0
            for it in items:
                existing = (
                    db.query(Tour)
                    .filter(
                        Tour.business_id == business.id,
                        Tour.title == it.title,
                        Tour.start_date.is_(None),
                    )
                    .first()
                )
                if existing:
                    existing.location = it.location or existing.location
                    existing.price = _parse_price_to_float(it.price) or existing.price
                    existing.url = it.url or existing.url
                    existing.last_updated = now
                else:
                    db.add(
                        Tour(
                            business_id=business.id,
                            title=it.title or "Untitled",
                            location=it.location,
                            start_date=None,
                            end_date=None,
                            price=_parse_price_to_float(it.price),
                            url=it.url,
                            last_updated=now,
                        )
                    )
                    created_count += 1

            source.last_run_at = now
            
            # If this was the first run (schedule_minutes=5), change to 24 hours
            if source.schedule_minutes == 5:
                source.schedule_minutes = 1440  # 24 hours
                print(f"[{now.isoformat()}] Source '{source.business_name}' schedule changed to 24 hours")
            
            db.commit()

            # Sync newly created tours to Firebase
            if created_count > 0:
                firebase_service.sync_business_tours_to_firebase(business.id, db)

            print(
                f"[{now.isoformat()}] Source '{source.business_name}' processed: {len(items)} items, {created_count} new"
            )
        except Exception as exc:  # noqa: BLE001
            db.rollback()
            print(f"[{now.isoformat()}] Error processing '{source.start_url}': {exc}")


def main() -> None:
    interval = int(os.getenv("WORKER_INTERVAL_SECONDS", "300"))
    print(f"[{datetime.now().isoformat()}] Worker starting with {interval}s interval")

    async def combined(db: Session) -> None:
        print(f"[{datetime.now().isoformat()}] Running scheduled job")
        await process_sources_job(db)
        # Run discovery if configured (temporarily disabled)
        # if os.getenv("SERPAPI_KEY"):
        #     await discover_au_tour_companies(db, max_pages=int(os.getenv("DISCOVERY_MAX_PAGES", "1")))

    asyncio.run(run_periodic(interval, combined))


if __name__ == "__main__":
    main()


