from datetime import date, datetime

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy import and_, select
from sqlalchemy.orm import Session

from ..db import get_db
from .. import models, schemas
from ..auth import require_api_key
from ..firebase_service import firebase_service


router = APIRouter(prefix="/api/tours", tags=["tours"])


@router.post("/", response_model=schemas.TourRead, dependencies=[Depends(require_api_key)])
def create_tour(payload: schemas.TourCreate, db: Session = Depends(get_db)):
    business = db.query(models.Business).get(payload.business_id)
    if not business:
        raise HTTPException(status_code=400, detail="Invalid business_id")
    tour = models.Tour(
        business_id=payload.business_id,
        title=payload.title,
        location=payload.location,
        start_date=payload.start_date,
        end_date=payload.end_date,
        price=payload.price,
        url=str(payload.url) if payload.url else None,
    )
    db.add(tour)
    db.commit()
    db.refresh(tour)

    # Sync to Firebase
    firebase_service.sync_tour_to_firebase(tour)

    return tour


@router.get("/", response_model=list[schemas.TourRead], dependencies=[Depends(require_api_key)])
def list_tours(
    location: str | None = None,
    min_date: date | None = Query(None, description="Start date on or after"),
    max_price: float | None = None,
    business_id: int | None = None,
    db: Session = Depends(get_db),
):
    query = db.query(models.Tour)
    if location:
        query = query.filter(models.Tour.location.ilike(f"%{location}%"))
    if min_date:
        query = query.filter(models.Tour.start_date >= min_date)
    if max_price is not None:
        query = query.filter(models.Tour.price != None).filter(models.Tour.price <= max_price)  # noqa: E711
    if business_id:
        query = query.filter(models.Tour.business_id == business_id)
    return query.order_by(models.Tour.start_date.asc().nulls_last()).limit(500).all()


@router.get("/{tour_id}", response_model=schemas.TourRead, dependencies=[Depends(require_api_key)])
def get_tour(tour_id: int, db: Session = Depends(get_db)):
    tour = db.query(models.Tour).get(tour_id)
    if not tour:
        raise HTTPException(status_code=404, detail="Not found")
    return tour


@router.get("/export/directory")
def export_tours_for_directory(db: Session = Depends(get_db)):
    """Export tours in a format optimized for directory apps - no API key required"""
    tours = db.query(models.Tour).join(models.Business).all()

    export_data = []
    for tour in tours:
        tour_data = {
            "id": tour.id,
            "title": tour.title,
            "location": tour.location,
            "tour_url": tour.url,
            "business_name": tour.business.name,
            "business_website": tour.business.website_url,
            "last_updated": tour.last_updated.isoformat(),
        }

        # Only include price if it exists
        if tour.price is not None:
            tour_data["price"] = float(tour.price)

        export_data.append(tour_data)

    return {
        "total_tours": len(export_data),
        "last_export": datetime.now().isoformat(),
        "tours": export_data
    }


