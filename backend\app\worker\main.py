from fastapi import FastAPI
from dotenv import load_dotenv

from .db import engine
from .models import Base
from .routers import tours, businesses
from .routers import sources
from .routers import discovery
from .ui import router as ui_router


load_dotenv()

app = FastAPI(title="B2B Data Exchange API", version="0.1.0")


@app.on_event("startup")
def on_startup() -> None:
    # Simple dev-time auto-migration. Replace with Alembic for production.
    Base.metadata.create_all(bind=engine)


@app.get("/health")
def health() -> dict[str, str]:
    return {"status": "ok"}


@app.get("/")
def root() -> dict[str, str]:
    return {"service": "B2B Data Exchange API", "version": "0.1.0"}


app.include_router(businesses.router)
app.include_router(tours.router)
app.include_router(sources.router)
app.include_router(discovery.router)
app.include_router(ui_router)


