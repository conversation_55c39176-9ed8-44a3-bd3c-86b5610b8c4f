from datetime import date

from .db import engine, SessionLocal, Base
from .models import Business, Tour


def run() -> None:
    Base.metadata.create_all(bind=engine)
    with SessionLocal() as db:
        # Upsert a sample business
        existing = db.query(Business).filter(Business.name == "Acme Tours").first()
        if not existing:
            biz = Business(
                name="Acme Tours",
                website_url="https://example.com",
                category="tours",
                description="Demo tour operator",
            )
            db.add(biz)
            db.flush()
        else:
            biz = existing

        sample_tour = db.query(Tour).filter(Tour.business_id == biz.id, Tour.title == "City Walk").first()
        if not sample_tour:
            db.add(
                Tour(
                    business_id=biz.id,
                    title="City Walk",
                    location="Adelaide",
                    start_date=date.today(),
                    end_date=None,
                    price=49.99,
                    url="https://example.com/tours/city-walk",
                )
            )
        db.commit()
    print("Seed completed")


if __name__ == "__main__":
    run()


