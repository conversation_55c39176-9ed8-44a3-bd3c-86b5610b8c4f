from fastapi import APIRouter, Request, Depends, Form, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session

from .db import get_db
from . import models


templates = Jinja2Templates(directory="app/templates")
router = APIRouter()


@router.get("/ui", response_class=HTMLResponse)
def ui_form(request: Request, db: Session = Depends(get_db)):
    sources = db.query(models.ScrapeSource).order_by(models.ScrapeSource.created_at.desc()).limit(50).all()
    return templates.TemplateResponse("ui_form.html", {"request": request, "sources": sources})


@router.post("/ui")
async def ui_submit(request: Request, db: Session = Depends(get_db)):
    form = await request.form()
    business_name = str(form.get("business_name", "")).strip()
    start_url = str(form.get("start_url", "")).strip()
    if business_name and start_url:
        # Create source with immediate processing (5 minutes), then 24 hours
        source = models.ScrapeSource(
            business_name=business_name, 
            start_url=start_url,
            schedule_minutes=5  # Process immediately, then every 24 hours
        )
        db.add(source)
        db.commit()
        
        # After first run, update to 24 hours
        # This will be handled by the scheduler after first processing
    return RedirectResponse(url="/ui", status_code=303)


@router.get("/ui/manage", response_class=HTMLResponse)
def manage_sources(request: Request, db: Session = Depends(get_db)):
    sources = db.query(models.ScrapeSource).order_by(models.ScrapeSource.created_at.desc()).all()
    return templates.TemplateResponse("manage_sources.html", {"request": request, "sources": sources})


@router.post("/ui/manage/{source_id}/delete")
async def delete_source_ui(source_id: int, db: Session = Depends(get_db)):
    try:
        source = db.query(models.ScrapeSource).get(source_id)
        if source:
            # Simply delete the source - let the database handle cascading
            db.delete(source)
            db.commit()
            print(f"Successfully deleted source {source_id}")
    except Exception as e:
        db.rollback()
        print(f"Error deleting source {source_id}: {e}")
        # Continue to redirect even if there's an error

    return RedirectResponse(url="/ui/manage", status_code=303)


@router.post("/ui/manage/{source_id}/toggle")
async def toggle_source_ui(source_id: int, db: Session = Depends(get_db)):
    source = db.query(models.ScrapeSource).get(source_id)
    if source:
        source.is_active = not source.is_active
        db.add(source)
        db.commit()
    return RedirectResponse(url="/ui/manage", status_code=303)


@router.post("/ui/manage/{source_id}/edit")
async def edit_source_ui(
    source_id: int,
    request: Request,
    db: Session = Depends(get_db)
):
    form = await request.form()
    source = db.query(models.ScrapeSource).get(source_id)
    if source:
        business_name = str(form.get("business_name", "")).strip()
        start_url = str(form.get("start_url", "")).strip()
        schedule_minutes = form.get("schedule_minutes")

        if business_name:
            source.business_name = business_name
        if start_url:
            source.start_url = start_url
        if schedule_minutes:
            try:
                source.schedule_minutes = int(schedule_minutes)
            except ValueError:
                pass  # Keep existing value if invalid

        db.add(source)
        db.commit()
    return RedirectResponse(url="/ui/manage", status_code=303)


