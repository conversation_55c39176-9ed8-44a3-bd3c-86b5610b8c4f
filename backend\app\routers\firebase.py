from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from ..auth import require_api_key
from ..db import get_db
from ..firebase_service import firebase_service


router = APIRouter(prefix="/api/firebase", tags=["firebase"], dependencies=[Depends(require_api_key)])


@router.post("/sync/all")
def sync_all_tours_to_firebase(db: Session = Depends(get_db)):
    """Manually sync all tours to Firebase"""
    if not firebase_service.is_enabled():
        raise HTTPException(status_code=503, detail="Firebase service not configured")
    
    synced_count = firebase_service.sync_all_tours_to_firebase(db)
    return {
        "message": f"Successfully synced {synced_count} tours to Firebase",
        "synced_count": synced_count
    }


@router.post("/sync/business/{business_id}")
def sync_business_tours_to_firebase(business_id: int, db: Session = Depends(get_db)):
    """Manually sync all tours for a specific business to Firebase"""
    if not firebase_service.is_enabled():
        raise HTTPException(status_code=503, detail="Firebase service not configured")
    
    synced_count = firebase_service.sync_business_tours_to_firebase(business_id, db)
    return {
        "message": f"Successfully synced {synced_count} tours for business {business_id} to Firebase",
        "business_id": business_id,
        "synced_count": synced_count
    }


@router.get("/status")
def firebase_status():
    """Check Firebase service status"""
    return {
        "firebase_enabled": firebase_service.is_enabled(),
        "app_id": firebase_service.app_id
    }
