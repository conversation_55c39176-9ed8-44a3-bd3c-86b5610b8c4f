from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from ..auth import require_api_key
from ..db import get_db
from ..firebase_service import firebase_service
from .. import models


router = APIRouter(prefix="/api/firebase", tags=["firebase"], dependencies=[Depends(require_api_key)])


@router.post("/sync/all")
def sync_all_tours_to_firebase(db: Session = Depends(get_db)):
    """Manually sync all tours to Firebase"""
    if not firebase_service.is_enabled():
        raise HTTPException(status_code=503, detail="Firebase service not configured")
    
    synced_count = firebase_service.sync_all_tours_to_firebase(db)
    return {
        "message": f"Successfully synced {synced_count} tours to Firebase",
        "synced_count": synced_count
    }


@router.post("/sync/business/{business_id}")
def sync_business_tours_to_firebase(business_id: int, db: Session = Depends(get_db)):
    """Manually sync all tours for a specific business to Firebase"""
    if not firebase_service.is_enabled():
        raise HTTPException(status_code=503, detail="Firebase service not configured")
    
    synced_count = firebase_service.sync_business_tours_to_firebase(business_id, db)
    return {
        "message": f"Successfully synced {synced_count} tours for business {business_id} to Firebase",
        "business_id": business_id,
        "synced_count": synced_count
    }


@router.get("/status")
def firebase_status():
    """Check Firebase service status"""
    return {
        "firebase_enabled": firebase_service.is_enabled(),
        "app_id": firebase_service.app_id
    }


@router.post("/fix-business-links")
def fix_scrape_source_business_links(db: Session = Depends(get_db)):
    """Fix ScrapeSource records that have business_id=null but should be linked to existing businesses"""

    # Find all ScrapeSource records with null business_id
    unlinked_sources = db.query(models.ScrapeSource).filter(
        models.ScrapeSource.business_id.is_(None)
    ).all()

    fixed_count = 0
    created_count = 0

    for source in unlinked_sources:
        # Try to find existing business by name
        existing_business = db.query(models.Business).filter(
            models.Business.name == source.business_name
        ).first()

        if existing_business:
            # Link to existing business
            source.business_id = existing_business.id
            fixed_count += 1
            print(f"Linked source '{source.business_name}' to existing business ID {existing_business.id}")
        else:
            # Create new business
            new_business = models.Business(
                name=source.business_name,
                website_url=source.start_url
            )
            db.add(new_business)
            db.flush()  # Get the ID
            source.business_id = new_business.id
            created_count += 1
            print(f"Created new business '{new_business.name}' with ID {new_business.id}")

    db.commit()

    return {
        "message": f"Fixed {fixed_count} existing links and created {created_count} new businesses",
        "linked_to_existing": fixed_count,
        "created_new_businesses": created_count,
        "total_processed": len(unlinked_sources)
    }
