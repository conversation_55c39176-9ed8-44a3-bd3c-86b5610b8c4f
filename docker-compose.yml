services:
  db:
    image: postgres:16
    restart: unless-stopped
    environment:
      POSTGRES_USER: appuser
      POSTGRES_PASSWORD: apppass
      POSTGRES_DB: appdb
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U appuser -d appdb"]
      interval: 5s
      timeout: 5s
      retries: 10

  backend:
    build:
      context: ./backend
    restart: unless-stopped
    environment:
      DATABASE_URL: postgresql+psycopg2://appuser:apppass@db:5432/appdb
      UVICORN_HOST: 0.0.0.0
      UVICORN_PORT: 8000
      PYTHONUNBUFFERED: "1"
      FIREBASE_SERVICE_ACCOUNT_PATH: /app/firebase-service-account.json
      APP_ID: ${APP_ID:-monoloci}

    depends_on:
      db:
        condition: service_healthy
    ports:
      - "8000:8000"
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  worker:
    build:
      context: ./backend
    restart: unless-stopped
    environment:
      DATABASE_URL: postgresql+psycopg2://appuser:apppass@db:5432/appdb
      WORKER_INTERVAL_SECONDS: 300
      PYTHONUNBUFFERED: "1"
      SERPAPI_KEY: ${SERPAPI_KEY:-}
      DISCOVERY_MAX_PAGES: ${DISCOVERY_MAX_PAGES:-1}
      DISCOVERY_SCHEDULE_MINUTES: ${DISCOVERY_SCHEDULE_MINUTES:-1440}
      FIREBASE_SERVICE_ACCOUNT_PATH: /app/firebase-service-account.json
      APP_ID: ${APP_ID:-monoloci}

    depends_on:
      db:
        condition: service_healthy
    command: python -m app.worker.scheduler

volumes:
  db_data:
