from __future__ import annotations

import os
from dataclasses import dataclass
from datetime import datetime
from typing import List
from urllib.parse import urlparse

import httpx
from sqlalchemy.orm import Session

from ..models import ScrapeSource


@dataclass
class WebResult:
    name: str
    url: str
    snippet: str


def _normalize_url(url: str) -> str:
    parsed = urlparse(url)
    if not parsed.scheme:
        return url
    return f"{parsed.scheme}://{parsed.netloc}{parsed.path}".rstrip("/")


async def serpapi_search(query: str, num: int = 10, start: int = 0) -> List[WebResult]:
    key = os.getenv("SERPAPI_KEY")
    if not key:
        return []
    params = {
        "engine": "google",
        "q": query,
        "location": "Australia",
        "hl": "en",
        "num": num,
        "start": start,
        "api_key": key,
    }
    async with httpx.AsyncClient(timeout=20.0) as client:
        resp = await client.get("https://serpapi.com/search.json", params=params)
        resp.raise_for_status()
        data = resp.json()
        results = []
        for item in data.get("organic_results", []) or []:
            title = item.get("title", "")
            link = item.get("link", "")
            snippet = item.get("snippet", "")
            if link:
                results.append(WebResult(name=title, url=link, snippet=snippet))
        return results


def _is_tour_operator_url(url: str, snippet: str = "") -> bool:
    """Check if URL and snippet indicate a direct tour operator (not aggregator)."""
    url_lower = url.lower()
    snippet_lower = snippet.lower()

    # Must contain tour-related path
    tour_paths = ['/tour', '/tours', '/experience', '/activities', '/packages']
    has_tour_path = any(path in url_lower for path in tour_paths)

    # Exclude aggregators and booking platforms
    aggregator_domains = [
        'tripadvisor', 'viator', 'getyourguide', 'expedia', 'booking.com',
        'airbnb', 'klook', 'tiqets', 'civitatis', 'musement', 'headout',
        'tourradar', 'gadventures', 'intrepidtravel', 'contiki'
    ]
    is_aggregator = any(domain in url_lower for domain in aggregator_domains)

    # Exclude job sites and social media
    excluded_terms = [
        'facebook.com', 'instagram.com', 'youtube.com', 'linkedin.com',
        'job', 'career', 'employment', 'recruit', 'hiring'
    ]
    has_excluded = any(term in url_lower for term in excluded_terms)

    # Look for direct operator indicators in snippet
    operator_indicators = [
        'we offer', 'our tours', 'book direct', 'family owned', 'locally owned',
        'established', 'since', 'years experience', 'specialist', 'expert'
    ]
    has_operator_indicator = any(indicator in snippet_lower for indicator in operator_indicators)

    return has_tour_path and not is_aggregator and not has_excluded and has_operator_indicator

async def discover_au_tour_companies(db: Session, max_pages: int = 3) -> int:
    """Find direct Australian tour operators with tour-specific URLs.

    Returns number of new sources created.
    """
    base_query = os.getenv(
        "DISCOVERY_QUERY",
        'inurl:tours OR inurl:tour "tour operator" OR "tours" Australia site:.au -job -career -tripadvisor -viator -getyourguide',
    )
    created = 0
    seen_urls = set(
        s.start_url for s in db.query(ScrapeSource).with_entities(ScrapeSource.start_url).all()
    )
    for page in range(max_pages):
        start_index = page * 10
        results = await serpapi_search(base_query, num=10, start=start_index)
        for r in results:
            url = _normalize_url(r.url)
            if not url or url in seen_urls:
                continue

            # Apply refined filtering
            if not _is_tour_operator_url(url, r.snippet):
                continue

            business_name = r.name.split("|")[0].split("–")[0].strip() or "Unknown"
            db.add(
                ScrapeSource(
                    business_name=business_name[:255],
                    start_url=url[:1024],
                    is_active=True,
                    schedule_minutes=int(os.getenv("DISCOVERY_SCHEDULE_MINUTES", "1440")),
                    created_at=datetime.now(),
                )
            )
            created += 1
            seen_urls.add(url)
        db.commit()
    if created:
        print(f"[{datetime.now().isoformat()}] Discovery created {created} new sources")
    return created


